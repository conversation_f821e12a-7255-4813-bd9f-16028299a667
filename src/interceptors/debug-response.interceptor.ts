import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { FastifyReply, FastifyRequest } from 'fastify';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { RequestContext, RequestContextService } from '../context/request-context.service';
import { LoggerService } from '../logger/logger.service';

@Injectable()
export class DebugResponseInterceptor implements NestInterceptor {
  constructor(private logger: LoggerService) {
    this.logger.setContext(DebugResponseInterceptor.name);
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<FastifyRequest>();

    const isDebugMode = this.extractDebugMode(request);

    if (!isDebugMode) {
      return next.handle();
    }

    const requestId = randomUUID();
    const requestContext: RequestContext = {
      requestId,
      isDebugMode,
      collectedLogs: []
    };

    const response = context.switchToHttp().getResponse<FastifyReply>();
    RequestContextService.runWithContext(requestContext, () => {
      (request as any).requestId = requestId;
      next.handle().pipe(
        tap({
          next: () => {
            this.addDebugHeaders(response);
          },
          error: () => {
            this.addDebugHeaders(response);
          }
        })
      );
    });

    return next.handle();
  }

  private addDebugHeaders(response: FastifyReply): void {
    const validationErrors = RequestContextService.getValidationErrors();

    if (validationErrors.length > 0) {
      const headerValue = `bad value: ${validationErrors.join(',')}`;
      response.header('x-tvn-debug-400', headerValue);
    }
  }

  private extractDebugMode(req: FastifyRequest): boolean {
    const query = req.query as any;
    return query?.isDebug === 'true' || query?.isDebug === true;
  }
}
