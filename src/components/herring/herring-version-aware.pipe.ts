import { PipeTransform } from '@nestjs/common';
import { getSchemaForVersion } from './herring.schema';
import { IHerringAPIGetParams, VersionEnum } from '../../types';
import Joi from 'joi';
import { handleJoiError } from '../../common/joi-error-handler';

export class HerringVersionAwarePipe implements PipeTransform<any, IHerringAPIGetParams> {
  transform(value: unknown): IHerringAPIGetParams {
    const versionSchema = Joi.object<{ version: VersionEnum }>({
      version: Joi.string()
        .default(VersionEnum.v_1_0_0)
        .valid(...Object.values(VersionEnum))
    });

    const { error: versionError, value: versionValue } = versionSchema.validate(value, {
      allowUnknown: true
    });

    if (versionError) {
      throw handleJoiError(versionError);
    }

    const schema = getSchemaForVersion(versionValue.version);

    const { error, value: validatedValue } = schema.validate(value, {
      abortEarly: false,
      convert: true,
      stripUnknown: false
    });

    if (error) {
      throw handleJoiError(error);
    }

    return validatedValue;
  }
}
