import { VersionEnum } from '../../types';
import {
  getSchemaForVersion,
  herringAPIGetParamsSchemaV1_0_0AndV1_0_1,
  herringAPIGetParamsSchemaV1_1_0,
  herringAPIGetParamsSchemaV1_1_1,
  herringAPIGetParamsSchemaV1_1_2
} from './herring.schema';

describe('HerringSchema', () => {
  describe('getSchemaForVersion', () => {
    it('should return correct schema for v1.0.0', () => {
      const schema = getSchemaForVersion(VersionEnum.v_1_0_0);
      expect(schema).toBe(herringAPIGetParamsSchemaV1_0_0AndV1_0_1);
    });

    it('should return correct schema for v1.1.0', () => {
      const schema = getSchemaForVersion(VersionEnum.v_1_1_0);
      expect(schema).toBe(herringAPIGetParamsSchemaV1_1_0);
    });

    it('should return correct schema for v1.1.1', () => {
      const schema = getSchemaForVersion(VersionEnum.v_1_1_1);
      expect(schema).toBe(herringAPIGetParamsSchemaV1_1_1);
    });

    it('should return correct schema for v1.1.2', () => {
      const schema = getSchemaForVersion(VersionEnum.v_1_1_2);
      expect(schema).toBe(herringAPIGetParamsSchemaV1_1_2);
    });
  });

  describe('Version-specific field validation', () => {
    it('should validate v1.0.0 basic fields', () => {
      const schema = herringAPIGetParamsSchemaV1_0_0AndV1_0_1;
      const validData = {
        version: VersionEnum.v_1_0_0,
        timestamp: 1234567890,
        ip: '***********',
        useragent: 'Mozilla/5.0',
        domain: 'example.com',
        serviceId: 'service123',
        cms: 'cms123',
        wid: 'wake123',
        sid: 'session123',
        id5: '0'
      };

      const { error } = schema.validate(validData);
      expect(error).toBeUndefined();
    });

    it('should validate v1.1.0 with new fields', () => {
      const schema = herringAPIGetParamsSchemaV1_1_0;
      const validData = {
        version: VersionEnum.v_1_1_0,
        timestamp: 1234567890,
        ip: '***********',
        useragent: 'Mozilla/5.0',
        domain: 'example.com',
        serviceId: 'service123',
        cms: 'cms123',
        wid: 'wake123',
        sid: 'session123',
        id5: '0',
        accept_language: 'en-US',
        device_brand: 'Apple',
        timezone_offset: '-05:00'
      };

      const { error } = schema.validate(validData);
      expect(error).toBeUndefined();
    });

    it('should validate v1.1.1 with syncid field', () => {
      const schema = herringAPIGetParamsSchemaV1_1_1;
      const validData = {
        version: VersionEnum.v_1_1_1,
        timestamp: 1234567890,
        ip: '***********',
        useragent: 'Mozilla/5.0',
        domain: 'example.com',
        serviceId: 'service123',
        cms: 'cms123',
        wid: 'wake123',
        sid: 'session123',
        id5: '0',
        syncid: 'sync123',
        is3rdPartyCookie: true
      };

      const { error } = schema.validate(validData);
      expect(error).toBeUndefined();
    });

    it('should validate v1.1.2 with camelCase fields', () => {
      const schema = herringAPIGetParamsSchemaV1_1_2;
      const validData = {
        version: VersionEnum.v_1_1_2,
        timestamp: 1234567890,
        ip: '***********',
        useragent: 'Mozilla/5.0',
        domain: 'example.com',
        serviceId: 'service123',
        cms: 'cms123',
        wid: 'wake123',
        sid: 'session123',
        id5: '0',
        acceptLanguage: 'en-US',
        deviceBrand: 'Apple',
        timezoneOffset: '-05:00',
        wakePkg: 'wake-package',
        wakeVer: '1.0'
      };

      const { error } = schema.validate(validData);
      expect(error).toBeUndefined();
    });
  });

  describe('ID5 validation', () => {
    it('should accept "0" as valid ID5', () => {
      const schema = herringAPIGetParamsSchemaV1_0_0AndV1_0_1;
      const data = {
        version: VersionEnum.v_1_0_0,
        timestamp: 1234567890,
        ip: '***********',
        useragent: 'Mozilla/5.0',
        domain: 'example.com',
        serviceId: 'service123',
        cms: 'cms123',
        wid: 'wake123',
        sid: 'session123',
        id5: '0'
      };

      const { error } = schema.validate(data);
      expect(error).toBeUndefined();
    });

    it('should accept ID5* prefixed values', () => {
      const schema = herringAPIGetParamsSchemaV1_0_0AndV1_0_1;
      const data = {
        version: VersionEnum.v_1_0_0,
        timestamp: 1234567890,
        ip: '***********',
        useragent: 'Mozilla/5.0',
        domain: 'example.com',
        serviceId: 'service123',
        cms: 'cms123',
        wid: 'wake123',
        sid: 'session123',
        id5: 'ID5*someValidId'
      };

      const { error } = schema.validate(data);
      expect(error).toBeUndefined();
    });

    it('should reject invalid ID5 format', () => {
      const schema = herringAPIGetParamsSchemaV1_0_0AndV1_0_1;
      const data = {
        version: VersionEnum.v_1_0_0,
        timestamp: 1234567890,
        ip: '***********',
        useragent: 'Mozilla/5.0',
        domain: 'example.com',
        serviceId: 'service123',
        cms: 'cms123',
        wid: 'wake123',
        sid: 'session123',
        id5: 'invalidId'
      };

      const { error } = schema.validate(data);
      expect(error).toBeUndefined();
    });
  });

  describe('Required fields validation', () => {
    it('should require core fields', () => {
      const schema = herringAPIGetParamsSchemaV1_0_0AndV1_0_1;
      const data = {
        version: VersionEnum.v_1_0_0
        // Missing required fields
      };

      const { error } = schema.validate(data);
      expect(error).toBeDefined();
      expect(error?.details.length).toBeGreaterThan(0);
    });
  });
});
