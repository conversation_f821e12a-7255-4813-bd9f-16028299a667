import { Controller, Get, HttpStatus, Query, Redirect, UseInterceptors } from '@nestjs/common';
import { env } from '../../env/envalidConfig';
import { DebugResponseInterceptor } from '../../interceptors/debug-response.interceptor';
import { QueryDelimiterInterceptor } from '../../interceptors/query-delimiter.interceptor';
import { LoggerService } from '../../logger/logger.service';
import { adSchema, IAdSchema } from './ad.schema';
import { AdService } from './ad.service';
import { JoiParamPipe } from './joi-param.pipe';

@Controller()
@UseInterceptors(DebugResponseInterceptor)
@UseInterceptors(new QueryDelimiterInterceptor('/'))
export class AdController {
  constructor(
    private readonly adService: AdService,
    private logger: LoggerService
  ) {
    this.logger.setContext(AdController.name);
  }

  @Get('ad.xml')
  @Redirect(env.AD_SERVER_URL, HttpStatus.TEMPORARY_REDIRECT)
  async redirectAd(
    @Query(new JoiParamPipe(adSchema)) requestParams: IAdSchema
  ): Promise<{ url: string }> {
    this.logger.log('REQUEST_START', { requestParams });

    const redirectUrl: string = await this.adService.getRedirectUrl(requestParams);

    this.logger.log('REQUEST_END', { redirectUrl });

    return { url: redirectUrl };
  }
}
