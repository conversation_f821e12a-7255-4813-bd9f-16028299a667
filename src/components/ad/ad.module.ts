import { Modu<PERSON> } from '@nestjs/common';
import { ContextModule } from '../../context/context.module';
import { LocalCacheModule } from '../../localCache/localCache.module';
import { LoggerModule } from '../../logger/logger.module';
import { RedisModule } from '../../redis/redis.module';
import { IdHelperService } from '../servicesWIthoutModule/idHelper.service';
import { AdController } from './ad.controller';
import { AdService } from './ad.service';
import { ID5DecoderService } from './id5decoder.service';

@Module({
  imports: [LocalCacheModule, LoggerModule, RedisModule, ContextModule],
  controllers: [AdController],
  providers: [ID5DecoderService, AdService, IdHelperService]
})
export class AdModule {}
